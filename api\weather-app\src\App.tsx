import { useState } from "react"

function App() {
  const [city , setcity] = useState("")
  const [weather , setweather] = useState(null)
  const [loading, setloading] = useState(false)
  const [error, seterror] = useState(false)

  const API_KEY = 'YOUR_API_KEY';

  const getWeather = () => {
    if (!city) return;

    setloading(true);
    seterror(false);
    setweather(null);

    fetch(`https://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${API_KEY}`)
      .then((res)=>{
        if (!res.ok) {
          throw new Error('city not found');
        }
        return res;
      })
      .then((response) => response.json())
      .then((data) => {
        setweather(data);
        setloading(false);
      })
      .catch((err) => {
        setloading(false);
        seterror(true);
      });

  }



  return (
    <>
    <div>
      <h1>Weather app</h1>
      <input type="text" placeholder='search' className="border border-black" onChange={(e) => setcity(e.target.value)} /> 
      <button type='submit' onClick={getWeather}>search</button>
    
    </div>
    </>
  )
}

export default App
